import { EnrollmentStatus } from '../entities/course-enrollment.entity';
import { PartialType } from '@nestjs/mapped-types';

export class CourseEnrollmentResponseDto {
  id: number;
  status: EnrollmentStatus;
  enrolledAt: Date;
  completedAt?: Date;
  lastAccessedAt?: Date;
  progressPercentage: number;
  completedLessons: number;
  totalLessons: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: number;
  courseId: number;
  
  // Optional nested objects
  user?: {
    id: number;
    name: string;
    email: string;
  };
  
  course?: {
    id: number;
    title: string;
    description?: string;
    thumbnailURL?: string;
  };
}
