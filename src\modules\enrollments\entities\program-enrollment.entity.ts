import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Program } from '../../programs/entities/program.entity';
import { EnrollmentStatus } from './course-enrollment.entity';

@Entity('program_enrollments')
@Index(['userId', 'programId'], { unique: true }) // Prevent duplicate enrollments
export class ProgramEnrollment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: EnrollmentStatus, default: EnrollmentStatus.ACTIVE })
  status: EnrollmentStatus;

  @Column({ type: 'timestamp with time zone' })
  enrolledAt: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  completedAt?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  lastAccessedAt?: Date;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0.00 })
  progressPercentage: number; // 0.00 to 100.00

  @Column({ type: 'int', default: 0 })
  completedCourses: number;

  @Column({ type: 'int', default: 0 })
  totalCourses: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Relationships
  @Column()
  userId: number;

  @Column()
  programId: number;

  @ManyToOne(() => User, (user) => user.programEnrollments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Program, (program) => program.enrollments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'programId' })
  program: Program;
}
