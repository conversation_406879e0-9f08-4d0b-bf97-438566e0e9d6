import { IsOptional, IsString, IsEnum, IsDateString, <PERSON>N<PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { EnrollmentStatus } from '../entities/course-enrollment.entity';
 
export class UpdateCourseEnrollmentDto {
  @IsOptional()
  @IsEnum(EnrollmentStatus)
  status?: EnrollmentStatus;

  @IsOptional()
  @IsDateString()
  completedAt?: string;

  @IsOptional()
  @IsDateString()
  lastAccessedAt?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  progressPercentage?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  completedLessons?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  totalLessons?: number;

  @IsOptional()
  @IsString()
  notes?: string;
}
